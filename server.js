"use strict";

const ESX = exports.es_extended.getSharedObject();

// قائمة المركبات المتاحة للإيجار مع الأسعار
const availableVehicles = [
    {
        id: 1,
        name: "دراجة نارية - Bati 801",
        model: "bati",
        price: 500,
        category: "دراجات نارية",
        image: "images/bati.png"
    },
    {
        id: 2,
        name: "دراجة نارية - PCJ-600",
        model: "pcj",
        price: 400,
        category: "دراجات نارية",
        image: "images/pcj.png"
    },
    {
        id: 3,
        name: "دراجة نارية - Sanchez",
        model: "sanchez",
        price: 300,
        category: "دراجات نارية",
        image: "images/sanchez.png"
    },
    {
        id: 4,
        name: "دراجة نارية - Aku<PERSON>",
        model: "akuma",
        price: 450,
        category: "دراجات نارية",
        image: "images/akuma.png"
    },
    {
        id: 5,
        name: "دراجة نارية - Double T",
        model: "double",
        price: 600,
        category: "دراجات نارية",
        image: "images/double.png"
    },
    {
        id: 6,
        name: "سيارة - Blista",
        model: "blista",
        price: 800,
        category: "سيارات",
        image: "images/blista.png"
    },
    {
        id: 7,
        name: "سيارة - Asea",
        model: "asea",
        price: 700,
        category: "سيارات",
        image: "images/asea.png"
    },
    {
        id: 8,
        name: "سيارة - Premier",
        model: "premier",
        price: 900,
        category: "سيارات",
        image: "images/premier.png"
    },
    {
        id: 9,
        name: "دراجة هوائية - BMX",
        model: "bmx",
        price: 100,
        category: "دراجات هوائية",
        image: "images/bmx.png"
    },
    {
        id: 10,
        name: "دراجة هوائية - Cruiser",
        model: "cruiser",
        price: 120,
        category: "دراجات هوائية",
        image: "images/cruiser.png"
    }
];

// Callback للحصول على قائمة المركبات
ESX.RegisterServerCallback('HyperScript_RentVehicle:callback-server', (source, cb, action, vehicleId) => {
    const xPlayer = ESX.GetPlayerFromId(source);
    
    if (!xPlayer) {
        cb(false);
        return;
    }

    switch (action) {
        case 'getVehicles':
            // إرسال قائمة المركبات المتاحة
            cb(availableVehicles);
            break;
            
        case 'startRent':
            // التعامل مع عملية الإيجار
            const vehicle = availableVehicles.find(v => v.id === vehicleId);
            
            if (!vehicle) {
                xPlayer.showNotification('~r~خطأ: المركبة غير موجودة');
                cb(false);
                return;
            }
            
            // التحقق من أموال اللاعب
            if (xPlayer.getMoney() >= vehicle.price) {
                // خصم المبلغ
                xPlayer.removeMoney(vehicle.price);
                
                // إرسال رسالة نجاح
                xPlayer.showNotification(`~g~تم استئجار ${vehicle.name} مقابل $${vehicle.price}`);
                
                // إرجاع بيانات المركبة للعميل
                cb({
                    model: vehicle.model,
                    name: vehicle.name,
                    price: vehicle.price
                });
            } else {
                // أموال غير كافية
                xPlayer.showNotification(`~r~أموال غير كافية! تحتاج إلى $${vehicle.price}`);
                cb(false);
            }
            break;
            
        default:
            cb(false);
            break;
    }
});

// Event للتعامل مع إرجاع المركبة (اختياري - يمكن إضافة مكافأة)
RegisterNetEvent('HyperScript_RentVehicle:returnVehicle');
onNet('HyperScript_RentVehicle:returnVehicle', (source) => {
    const xPlayer = ESX.GetPlayerFromId(source);
    
    if (xPlayer) {
        // يمكن إضافة مكافأة صغيرة عند الإرجاع
        const returnBonus = 50;
        xPlayer.addMoney(returnBonus);
        xPlayer.showNotification(`~g~تم إرجاع المركبة وحصلت على مكافأة $${returnBonus}`);
    }
});

// Event للتحقق من حالة اللاعب عند الاتصال
RegisterNetEvent('HyperScript_RentVehicle:checkPlayerStatus');
onNet('HyperScript_RentVehicle:checkPlayerStatus', (source) => {
    const xPlayer = ESX.GetPlayerFromId(source);
    
    if (xPlayer) {
        console.log(`[HyperScript_RentVehicle] Player ${xPlayer.getName()} (${source}) connected with $${xPlayer.getMoney()}`);
    }
});

// تسجيل الأحداث في الكونسول
console.log('[HyperScript_RentVehicle] Server script loaded successfully');
console.log(`[HyperScript_RentVehicle] ${availableVehicles.length} vehicles available for rent`);
