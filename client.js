"use strict";

const state = {
    isOpen: false,
    runClose: false,
    current: null,
    hasRentedBike: false,
    rentedVehicleId: null,
    notificationShownForAttempt: false,
    places: [
        { x: -1085.78, y: -263.01, z: 37.80, spwan: { x: -1085.78, y: -263.01, z: 37.80, h: 0.0 } },
        { x: 2052.05, y: 3306.38, z: 45.79, spwan: { x: 2052.05, y: 3306.38, z: 45.79, h: 0.0 } }, 
        { x: -1262.36, y: -1438.98, z: 4.45, spwan: { x: -1262.36, y: -1438.98, z: 4.45, h: 0.0 } },
        { x: -242.0609, y: -989.6091, z: 29.2878, spwan: { x: -242.0609, y: -989.6091, z: 29.2878, h: 0.0 } },
        { x: -1028.36, y: -2740.76, z: 20.17, spwan: { x: -1028.36, y: -2740.76, z: 20.17, h: 0.0 } },
        { x: 1781.31, y: 3321.45, z: 41.36, spwan: { x: 1781.31, y: 3321.45, z: 41.36, h: 0.0 } },
        { x: 412.82, y: -1615.41, z: 29.35, spwan: { x: 412.82, y: -1615.41, z: 29.35, h: 0.0 } },
        { x: 218.4954, y: -1451.16, z: 29.287, spwan: { x: 218.4954, y: -1451.16, z: 29.287, h: 0.0 } },
        { x: 149.7633, y: 6640.8428, z: 31.5720, spwan: { x: 149.7633, y: 6640.8428, z: 31.5720, h: 135.5552 } },
        { x: 2136.61, y: 4791.93, z: 40.99, spwan: { x: 2136.61, y: 4791.93, z: 40.99, h: 0.0 } },
        { x: 1737.42, y: 3710.33, z: 34.17, spwan: { x: 1737.42, y: 3710.33, z: 34.17, h: 0.0 } },
        { x: 1855.1, y: 2582.22, z: 45.69, spwan: { x: 1855.1, y: 2582.22, z: 45.69, h: 0.0 } },
        { x: 299.6, y: -1450.45, z: 29.97, spwan: { x: 299.6, y: -1450.45, z: 29.97, h: 0.0 } }, 
        { x: -62.73, y: -1081.91, z: 26.78, spwan: { x: -62.73, y: -1081.91, z: 26.78, h: 0.0 } },
        { x: -429.7, y: 6035.6, z: 31.51, spwan: { x: -429.7, y: 6035.6, z: 31.51, h: 0.0 } },
        { x: 411.25, y: -965.01, z: 29.39, spwan: { x: 411.25, y: -965.01, z: 29.39, h: 0.0 } }, 
        { x: -482.37, y: -337.95, z: 34.50, spwan: { x: -482.37, y: -337.95, z: 34.50, h: 0.0 } },
        { x: 2195.38, y: 2916.37, z: 46.53, spwan: { x: 2195.38, y: 2916.37, z: 46.53, h: 0.0 } }, 
        { x: 1200.43, y: 2690.2, z: 37.66, spwan: { x: 1200.43, y: 2690.2, z: 37.66, h: 0.0 } },
        { x: -1814.02, y: 791.8, z: 137.87, spwan: { x: -1814.02, y: 791.8, z: 137.87, h: 0.0 } },
        { x: 2567.44, y: 328.0, z: 108.45, spwan: { x: 2567.44, y: 328.0, z: 108.45, h: 0.0 } },
        { x: 1311.2, y: 4306.41, z: 37.83, spwan: { x: 1311.2, y: 4306.41, z: 37.83, h: 0.0 } },
        { x: -215.2, y: 6554.44, z: 10.99, spwan: { x: -215.2, y: 6554.44, z: 10.99, h: 0.0 } },
        { x: -1575.15, y: 5172.32, z: 19.58, spwan: { x: -1575.15, y: 5172.32, z: 19.58, h: 0.0 } },
        { x: -732.73, y: -1299.85, z: 5.05, spwan: { x: -732.73, y: -1299.85, z: 5.05, h: 0.0 } },
        { x: 49.29, y: -2757.6, z: 6.0, spwan: { x: 49.29, y: -2757.6, z: 6.0, h: 0.0 } },
        { x: 243.37, y: 199.31, z: 105.21, spwan: { x: 243.37, y: 199.31, z: 105.21, h: 0.0 } },
        { x: 3830.56, y: 4459.18, z: 2.56, spwan: { x: 3830.56, y: 4459.18, z: 2.56, h: 0.0 } },
        { x: 2568.03, y: 2716.66, z: 42.77, spwan: { x: 2568.03, y: 2716.66, z: 42.77, h: 0.0 } },
        { x: -1128.28, y: 2673.59, z: 18.11, spwan: { x: -1128.28, y: 2673.59, z: 18.11, h: 0.0 } },
        { x: 664.71, y: -4.82, z: 84.09, spwan: { x: 664.71, y: -4.82, z: 84.09, h: 0.0 } },
        { x: 2780.61, y: -707.28, z: 5.1, spwan: { x: 2780.61, y: -707.28, z: 5.1, h: 0.0 } },
        { x: 745.33, y: -2559.07, z: 19.69, spwan: { x: 745.33, y: -2559.07, z: 19.69, h: 0.0 } },
        { x: 821.92, y: -3017.24, z: 6.02, spwan: { x: 821.92, y: -3017.24, z: 6.02, h: 0.0 } },
        { x: -245.1543, y: 6273.6763, z: 31.4790, spwan: { x: -245.1543, y: 6273.6763, z: 31.4790, h: 0.0 } },
        { x: 1171.92, y: -1518.8, z: 34.84, spwan: { x: 1171.92, y: -1518.8, z: 34.84, h: 0.0 } },
        { x: 320.16, y: -1371.09, z: 31.91, spwan: { x: 320.16, y: -1371.09, z: 31.91, h: 0.0 } },
        { x: -325.62, y: 6067.19, z: 31.28, spwan: { x: -325.62, y: 6067.19, z: 31.28, h: 0.0 } },
        { x: -230.94, y: 6318.99, z: 31.48, spwan: { x: -230.94, y: 6318.99, z: 31.48, h: 0.0 } },
        { x: 1507.89, y: 779.96, z: 77.44, spwan: { x: 1507.89, y: 779.96, z: 77.44, h: 0.0 } },
        { x: 310.55, y: -1140.6, z: 29.42, spwan: { x: 310.55, y: -1140.6, z: 29.42, h: 0.0 } },
        { x: -1203.03, y: -2077.89, z: 14.07, spwan: { x: -1203.03, y: -2077.89, z: 14.07, h: 0.0 } },
        { x: -987.34, y: -2412.23, z: 13.94, spwan: { x: -987.34, y: -2412.23, z: 13.94, h: 0.0 } },
        { x: 1235.16, y: 3545.80, z: 35.14, spwan: { x: 1235.16, y: 3545.80, z: 35.14, h: 0.0 } },
        { x: -565.06, y: 5382.22, z: 69.78, spwan: { x: -565.06, y: 5382.22, z: 69.78, h: 0.0 } },
        { x: -89.30, y: 6304.55, z: 31.33, spwan: { x: -89.30, y: 6304.55, z: 31.33, h: 0.0 } },
        { x: -2665.47, y: 2503.63, z: 16.69, spwan: { x: -2665.47, y: 2503.63, z: 16.69, h: 0.0 } },
        { x: 186.14, y: -2544.93, z: 6.00, spwan: { x: 186.14, y: -2544.93, z: 6.00, h: 0.0 } },
        { x: -1.7334, y: 30.2195, z: 71.15, spwan: { x: -1.7334, y: 30.2195, z: 71.15, h: 0.0 } },
        { x: 880.35, y: 31.4758, z: 78.66, spwan: { x: 880.35, y: 31.4758, z: 78.66, h: 0.0 } },
        { x: -43.14, y: -776.89, z: 44.22, spwan: { x: -43.14, y: -776.89, z: 44.22, h: 0.0 } },
        { x: 416.58, y: -793.46, z: 29.37, spwan: { x: 416.58, y: -793.46, z: 29.37, h: 0.0 } },
        { x: 85.889, y: 6478.69, z: 31.39, spwan: { x: 85.889, y: 6478.69, z: 31.39, h: 0.0 } },
        { x: 1551.91, y: 2209.81, z: 78.71, spwan: { x: 1551.91, y: 2209.81, z: 78.71, h: 0.0 } },
        { x: 229.76, y: -1146.11, z: 29.3, spwan: { x: 229.76, y: -1146.11, z: 29.3, h: 0.0 } },
        { x: 374.5871, y: -383.7, z: 46.4951, spwan: { x: 374.5871, y: -383.7, z: 46.4651, h: 0.0 } },
        { x: 345.6848, y: -1559.0033, z: 29.2916, spwan: { x: 345.6848, y: -1559.0033, z: 29.2916, h: 314.7461 } },
        { x: 295.0337, y: -1593.2725, z: 30.5321, spwan: { x: 295.0337, y: -1593.2725, z: 30.5321, h: 319.0193 } },
        { x: 855.1384, y: -1562.4037, z: 30.0903, spwan: { x: 855.1384, y: -1562.4037, z: 30.0903, h: 0 } },
    ]
};

const ESX = exports.es_extended.getSharedObject();
const empty = null;

AddEventHandler('playerSpawned', () => {
    state.hasRentedBike = false;
    state.notificationShownForAttempt = false;
    state.rentedVehicleId = null;
});

for (const item of state.places) {}

setTick(() => {
    const pedId = PlayerPedId();
    const coords = GetEntityCoords(PlayerPedId(), true);

    for (let item of state.places) {
        const distance = GetDistanceBetweenCoords(coords[0], coords[1], coords[2], item.x, item.y, item.z, true);
        if (distance < 30) {
            DrawMarker(38, item.x, item.y, item.z, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.2, 1.2, 1, 227, 161, 28, 100, false, true, 2, false, empty, empty, false);
            DrawMarker(27, item.x, item.y, item.z - 0.98, 0, 0, 0, 0, 0, 0, 1.5, 1.5, 0, 227, 161, 28, 100, false, true, 2, false, empty, empty, false);
            if (distance < 0.8) {
                state.current = item;
                break;
            }
            else state.current = null;
        }
        else state.current = null;
    }

    const isInVehicle = IsPedInAnyVehicle(pedId, false);

    if (state.current && !IsPauseMenuActive()) {
        
        if (IsControlJustPressed(0, 38)) {
            if (state.hasRentedBike && isInVehicle) {
                const vehicle = GetVehiclePedIsIn(pedId, false);
                if (vehicle === state.rentedVehicleId) {
                    exports.AdvancedParking.DeleteVehicle(vehicle, false);
                    DeleteVehicle(vehicle);
                    state.hasRentedBike = false;
                    state.rentedVehicleId = null;
                    exports.OscarCounty_Notifications.showAttention('success', 'تم إرجاع الدراجة بنجاح');
                } else {
                    exports.OscarCounty_Notifications.showAttention('error', 'هذه ليست الدراجة التي استأجرتها');
                }
            } else if (!isInVehicle) {
                // Renting a new bike
                ESX.TriggerServerCallback('HyperScript_RentVehicle:callback-server', (items) => {
                    SetNuiFocus(true, true);
                    state.isOpen = true;
                    SendNUIMessage(JSON.stringify({ type: 'openUI', items }));
                }, 'getVehicles');
            }
        }
        else if (!state.isOpen && !state.runClose) {
            SendNUIMessage(JSON.stringify({ type: 'entranceOpen' }));
        }
        state.runClose = true;
    }
    else if (state.runClose) {
        closeUI(true);
    }
});

function closeUI(withNUI) {
    if (withNUI)
        SendNUIMessage(JSON.stringify({ type: 'closeUI', value: false }));
    SetNuiFocus(false, false);
    state.isOpen = false;
    state.runClose = false;
}

RegisterNuiCallbackType('NUI:update');
on('__cfx_nui:NUI:update', (data, cb) => {
    switch (data.type) {
        case 'startRent':
            if (!state.hasRentedBike) {
                ESX.TriggerServerCallback('HyperScript_RentVehicle:callback-server', (data) => {
                    if (data) {
                        ESX.Game.SpawnVehicle(data.model, state.current?.spwan, state.current?.spwan.h, (vehId) => {
                            SetVehRadioStation(vehId, 'OFF');
                            TaskWarpPedIntoVehicle(PlayerPedId(), vehId, -1);
                            SetVehicleNumberPlateText(vehId, GetPlayerServerId(PlayerId()).toString());
                            state.hasRentedBike = true;
                            state.rentedVehicleId = vehId;
                        }, true);
                    }
                }, data.type, data.id);
                closeUI(true);
            }
            break;
        default:
            closeUI(false);
    }
    cb('OK!');
});
